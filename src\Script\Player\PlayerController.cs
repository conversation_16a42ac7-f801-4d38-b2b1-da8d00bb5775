/*
 * 玩家控制器 - 等距视角移动系统
 */

using Godot;
using ArchipelagoGame.Interfaces;

namespace ArchipelagoGame.Player
{
    /// <summary>
    /// 玩家控制器 - 实现等距视角的角色移动
    /// 继承自CharacterBody2D以获得物理移动和碰撞检测功能
    /// </summary>
    public partial class PlayerController : CharacterBody2D
    {

        [Export] public float Speed = 300.0f; // 移动速度（像素/秒）
        public AnimationPlayer PlayerAnimation;

        public override void _Ready()
        {
            // 添加到玩家组，用于场景切换时的位置管理
            AddToGroup("player");           
            // 获取动画播放器节点
            PlayerAnimation = GetNode<AnimationPlayer>("AnimationPlayer");                        
        }


        public override void _PhysicsProcess(double delta)
        {
            // 1. 获取输入方向（-1到1之间的值）
            Vector2 inputDirection = Input.GetVector("ui_left", "ui_right", "ui_up", "ui_down");

            // 2. 设置速度并移动
            Velocity = inputDirection * Speed;
            MoveAndSlide();

            // 3. 根据移动方向播放动画
            if (inputDirection != Vector2.Zero && PlayerAnimation != null)
            {
                   string animationName = GetDirectionAnimation(inputDirection);

                        // 只有当动画改变时才播放新动画，避免重复播放
                        if (PlayerAnimation.CurrentAnimation != animationName)
                        {
                            PlayerAnimation.Play(animationName);
                        }
                
            }
            else if (PlayerAnimation != null)
            {
                // 停止移动时停止动画
                if (PlayerAnimation.IsPlaying())
                {
                    PlayerAnimation.Stop();
                }
            }
        }

        /// <summary>
        /// 根据输入方向获取对应的动画名称
        /// </summary>
        private static string GetDirectionAnimation(Vector2 direction)
        {
            // 8方向动画映射
            if (direction.Y < -0.5f) // 向上
            {
                if (direction.X < -0.5f)
                    return "up_left_move";
                else if (direction.X > 0.5f)
                    return "up_right_move";
                else
                    return "up_move";
            }
            else if (direction.Y > 0.5f) // 向下
            {
                if (direction.X < -0.5f)
                    return "down_left_move";
                else if (direction.X > 0.5f)
                    return "down_right_move";
                else
                    return "down_move";
            }
            else // 水平移动
            {
                if (direction.X < 0)
                    return "left_move";
                else
                    return "right_move";
            }
        }
    }
}
